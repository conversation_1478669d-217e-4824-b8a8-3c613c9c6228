import cv2
import time

def diagnose_camera(index=0):
    """诊断摄像头问题"""
    print(f"开始诊断摄像头 {index}...")
    
    # 尝试不同后端
    backends = [
        (cv2.CAP_DSHOW, "DirectShow"),
        (cv2.CAP_MSMF, "MSMF"),
        (-1, "Auto")
    ]
    
    for backend, name in backends:
        print(f"\n尝试后端: {name}")
        try:
            if backend == -1:
                cap = cv2.VideoCapture(index)
            else:
                cap = cv2.VideoCapture(index, backend)
            
            if not cap.isOpened():
                print(f"  无法打开摄像头 (后端: {name})")
                continue
                
            print(f"  摄像头已打开 (后端: {name})")
            
            # 获取摄像头属性
            width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
            height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)
            fps = cap.get(cv2.CAP_PROP_FPS)
            print(f"  默认分辨率: {width}x{height}")
            print(f"  默认FPS: {fps}")
            
            # 尝试读取帧
            for i in range(10):
                ret, frame = cap.read()
                if ret:
                    print(f"  成功读取第 {i+1} 帧，尺寸: {frame.shape}")
                    # 尝试写入文件进行验证
                    cv2.imwrite(f'test_frame_{name}_{i}.jpg', frame)
                    break
                else:
                    print(f"  无法读取第 {i+1} 帧")
                    time.sleep(0.2)
            
            cap.release()
            
        except Exception as e:
            print(f"  使用后端 {name} 时出错: {e}")
    
    print("\n诊断完成")

def list_cameras():
    """列出所有可用摄像头"""
    print("检测可用摄像头...")
    index = 0
    cameras = []
    
    while True:
        cap = cv2.VideoCapture(index)
        if not cap.isOpened():
            cap.release()
            break
        
        cameras.append(index)
        print(f"发现摄像头 {index}")
        cap.release()
        index += 1
    
    if not cameras:
        print("未发现可用摄像头")
    else:
        print(f"总共发现 {len(cameras)} 个摄像头")
    
    return cameras

if __name__ == "__main__":
    print("摄像头诊断工具")
    print("=" * 30)
    
    # 列出所有摄像头
    cameras = list_cameras()
    
    if cameras:
        # 诊断第一个摄像头
        diagnose_camera(cameras[0])