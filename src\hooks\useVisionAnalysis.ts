// src/hooks/useVisionAnalysis.ts

interface VisionAnalysisResult {
  description: string;
  clothing_items?: string[];
  colors?: string[];
  style?: string;
  confidence?: number;
}

// 智谱AI GLM-4.5V API配置
declare const process: {
  env: {
    VITE_ZHIPU_API_KEY?: string;
    VITE_ZHIPU_API_URL?: string;
  };
};

const ZHIPU_API_KEY = process.env.VITE_ZHIPU_API_KEY || '145473c3783342a4b45cc425d8d79270.Xe5vsUQYxF0nzCZH';
const ZHIPU_API_URL = process.env.VITE_ZHIPU_API_URL || 'https://open.bigmodel.cn/api/paas/v4/chat/completions';

const useVisionAnalysis = () => {
  // 调用智谱GLM-4.5V视觉模型API
  const analyzeImage = async (imageData: string): Promise<VisionAnalysisResult> => {
    // 检查是否有图像数据
    if (!imageData) {
      console.warn('没有提供图像数据用于视觉分析');
      return {
        description: "用户看起来很棒！",
        clothing_items: [],
        colors: [],
        style: "casual",
        confidence: 0
      };
    }
    
    try {
      console.log('调用视觉分析API，图像大小:', imageData.length);
      
      // 移除数据URL前缀
      const base64Data = imageData.split(',')[1];
      
      // 准备请求数据
      const requestData = {
        model: "glm-4v", // 智谱AI GLM-4V模型
        messages: [
          {
            role: "user",
            content: [
              {
                type: "image_url",
                image_url: {
                  url: imageData
                }
              },
              {
                type: "text",
                text: "请分析这张图片中的人物穿着和整体外观。请以以下JSON格式回复: {\"description\": \"整体描述\", \"clothing_items\": [\"衣物1\", \"衣物2\"], \"colors\": [\"颜色1\", \"颜色2\"], \"style\": \"风格\"}"
              }
            ]
          }
        ],
        stream: false
      };

      // 调用智谱AI GLM-4V API
      const response = await fetch(ZHIPU_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${ZHIPU_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('视觉分析API响应:', result);
      
      // 解析响应并返回结果
      if (result.choices && result.choices[0] && result.choices[0].message) {
        try {
          // 尝试解析返回的JSON内容
          const content = result.choices[0].message.content;
          const parsedResult = JSON.parse(content);
          
          return {
            description: parsedResult.description || "用户看起来很棒！",
            clothing_items: parsedResult.clothing_items || [],
            colors: parsedResult.colors || [],
            style: parsedResult.style || "casual",
            confidence: parsedResult.confidence || 0.8
          };
        } catch (parseError) {
          console.error('解析API响应失败:', parseError);
          // 如果解析失败，返回默认结果
          return {
            description: result.choices[0].message.content || "用户看起来很棒！",
            clothing_items: [],
            colors: [],
            style: "casual",
            confidence: 0.5
          };
        }
      } else {
        throw new Error('API响应格式不正确');
      }
    } catch (error) {
      console.error('视觉分析API调用失败:', error);
      // 降级到模拟响应
      return {
        description: "用户看起来精神饱满，充满自信。",
        clothing_items: [],
        colors: [],
        style: "casual",
        confidence: 0.5
      };
    }
  };

  return { analyzeImage };
};

export default useVisionAnalysis;