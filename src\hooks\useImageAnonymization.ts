// src/hooks/useImageAnonymization.ts

const useImageAnonymization = () => {
  // 使用OpenCV服务进行图像匿名化
  const anonymizeImage = async (imageData: string): Promise<string> => {
    // 如果没有图像数据，返回空字符串
    if (!imageData) {
      console.warn('没有提供图像数据');
      return "";
    }
    
    try {
      console.log('开始图像匿名化处理...');
      const response = await fetch('http://127.0.0.1:5000/anonymize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ image_data: imageData }),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.anonymized_image) {
        console.log('图像匿名化成功，处理后的图像大小:', data.anonymized_image.length);
        return data.anonymized_image;
      } else if (data.error) {
        console.error('匿名化服务返回错误:', data.error);
        return imageData; // 失败时返回原图
      } else {
        console.error('匿名化服务返回无效响应');
        return imageData; // 失败时返回原图
      }
    } catch (error) {
      console.error('OpenCV服务连接失败:', error);
      // 在开发环境下，可以返回模拟的匿名化图像
      if (process.env.NODE_ENV === 'development') {
        console.log('开发模式：使用模拟匿名化图像');
        return imageData; // 开发模式下直接返回原图
      }
      return imageData; // 失败时返回原图
    }
  };

  return { anonymizeImage };
};

export default useImageAnonymization;