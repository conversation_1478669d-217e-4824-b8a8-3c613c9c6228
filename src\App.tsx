// src/App.tsx
import React, { useState, useEffect, useRef } from 'react';
import StandbyDisplay from './components/StandbyDisplay';
import DanmuDisplay from './components/DanmuDisplay';
import CameraPreview from './components/CameraPreview';
import useHumanDetection from './hooks/useHumanDetection';
import useImageAnonymization from './hooks/useImageAnonymization';
import useVisionAnalysis from './hooks/useVisionAnalysis';
import usePraiseGenerator from './hooks/usePraiseGenerator';

function App() {
  const [isStandby, setIsStandby] = useState(true);
  const [praises, setPraises] = useState<string[]>([]);
  const [showCameraPreview, setShowCameraPreview] = useState(true); // 默认显示摄像头预览
  const [isHumanDetected, setIsHumanDetected] = useState(false); // 跟踪是否检测到人体
  const captureCountRef = useRef(0); // 跟踪捕获次数
  
  const { anonymizeImage } = useImageAnonymization();
  const { analyzeImage } = useVisionAnalysis();
  const { generatePraise } = usePraiseGenerator();
  
  // 从后端API捕获当前帧
  const captureImage = async (): Promise<string> => {
    try {
      const response = await fetch('http://127.0.0.1:5000/capture_frame');
      const data = await response.json();

      if (data.success && data.image_data) {
        console.log('成功从后端获取图像帧');
        return data.image_data;
      } else {
        console.error('后端返回错误:', data.error);
        return "";
      }
    } catch (error) {
      console.error('捕获图像失败:', error);
      return "";
    }
  };
  
  const handleHumanDetected = async () => {
    console.log("handleHumanDetected 被调用");
    setIsHumanDetected(true); // 标记检测到人体
    
    // 只有在待机状态下才切换到弹幕显示模式
    if (isStandby) {
      setIsStandby(false);
      setPraises([]); // 清空之前的弹幕
      captureCountRef.current = 0; // 重置捕获计数
    }
    
    // 每次检测到人体时增加捕获计数
    captureCountRef.current++;

    // 每第3次检测执行图像捕获和分析（控制频率）
    if (captureCountRef.current % 3 === 1) {
      // 等待一小段时间确保画面稳定
      await new Promise(resolve => setTimeout(resolve, 300));

      // 捕获图像
      const imageData = await captureImage();
      console.log("图像捕获完成，大小:", imageData.length);

      // Step 1: Anonymize image
      const anonymizedImage = await anonymizeImage(imageData);

      // Step 2: Analyze image
      const visionResult = await analyzeImage(anonymizedImage);

      // Step 3: Generate praises
      const newPraises = generatePraise(visionResult);
      console.log("生成的赞美:", newPraises);

      // 添加新赞美到现有弹幕列表
      setPraises(prev => [...prev, ...newPraises]);
    }
  };
  
  // 处理人体未检测到的情况
  const handleHumanNotDetected = () => {
    console.log("未检测到人体");
    setIsHumanDetected(false);
    
    // 只有在非待机状态且未检测到人体时才切换回待机模式
    if (!isStandby) {
      setIsStandby(true);
      setPraises([]);
      captureCountRef.current = 0; // 重置捕获计数
    }
  };
  
  // 组件初始化
  useEffect(() => {
    console.log('智能夸夸镜应用已启动');
  }, []);
  
  // 使用人体检测hook，但添加人体未检测到的处理
  useHumanDetection(() => {
    handleHumanDetected();
    
    // 设置一个定时器来检查人体是否仍然存在
    const checkInterval = setInterval(async () => {
      try {
        const response = await fetch('http://127.0.0.1:5000/detection_status');
        const data = await response.json();
        
        if (!data.human_detected) {
          clearInterval(checkInterval);
          handleHumanNotDetected();
        }
      } catch (error) {
        console.error('检查人体状态失败:', error);
        clearInterval(checkInterval);
        handleHumanNotDetected();
      }
    }, 1000); // 每秒检查一次人体状态
  });

  return (
    <div className="w-screen h-screen overflow-hidden">
      {isStandby ? (
        <>
          <StandbyDisplay />
          <CameraPreview isActive={showCameraPreview} />
        </>
      ) : (
        <>
          <DanmuDisplay praises={praises} />
          <CameraPreview isActive={showCameraPreview} />
        </>
      )}
    </div>
  );
}

export default App;