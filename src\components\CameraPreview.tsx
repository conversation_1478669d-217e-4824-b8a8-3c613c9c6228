import React, { useState, useEffect, useRef } from 'react';

interface CameraPreviewProps {
  isActive: boolean;
}

const CameraPreview: React.FC<CameraPreviewProps> = ({ isActive }) => {
  const imgRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 连接到后端视频流
  useEffect(() => {
    if (!isActive) return;

    const connectToVideoStream = () => {
      try {
        // 清除之前的错误信息
        setError(null);

        if (imgRef.current) {
          // 设置视频流源
          imgRef.current.src = 'http://127.0.0.1:5000/video_stream';

          // 监听图像加载事件
          imgRef.current.onload = () => {
            setIsConnected(true);
            console.log('视频流连接成功');
          };

          // 监听图像加载错误
          imgRef.current.onerror = (err) => {
            console.error('视频流连接失败:', err);
            setError('无法连接到摄像头服务');
            setIsConnected(false);

            // 尝试重新连接
            if (retryTimeoutRef.current) {
              clearTimeout(retryTimeoutRef.current);
            }
            retryTimeoutRef.current = setTimeout(() => {
              if (isActive) {
                console.log('尝试重新连接视频流...');
                connectToVideoStream();
              }
            }, 2000);
          };
        }
      } catch (err) {
        console.error('连接视频流时出错:', err);
        setError('连接摄像头服务失败');
        setIsConnected(false);
      }
    };

    connectToVideoStream();

    // 清理函数
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      if (imgRef.current) {
        imgRef.current.src = '';
        imgRef.current.onload = null;
        imgRef.current.onerror = null;
      }
      setIsConnected(false);
    };
  }, [isActive]);

  if (!isActive) {
    return null;
  }

  return (
    <div className="absolute top-4 right-4 w-80 h-60 bg-black rounded-lg overflow-hidden border-2 border-white">
      {error ? (
        <div className="w-full h-full flex flex-col items-center justify-center text-red-500 text-sm p-2 text-center">
          <div>{error}</div>
          <div className="mt-2 text-xs text-gray-400">请确保OpenCV服务正在运行</div>
        </div>
      ) : (
        <>
          <img
            ref={imgRef}
            alt="摄像头预览"
            className="w-full h-full object-cover"
          />
          <canvas
            ref={canvasRef}
            width={320}
            height={240}
            className="absolute top-0 left-0 w-full h-full pointer-events-none"
          />
        </>
      )}
      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
        {isConnected ? '摄像头预览' : '连接中...'}
      </div>
    </div>
  );
};

export default CameraPreview;