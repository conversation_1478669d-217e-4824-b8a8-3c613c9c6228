#!/usr/bin/env python3
"""
简化的视频流服务器，用于测试摄像头资源冲突修复
"""

import cv2
import time
from flask import Flask, Response, jsonify
from flask_cors import CORS
import threading
import base64

app = Flask(__name__)
CORS(app)

class SimpleCameraManager:
    def __init__(self):
        self.cap = None
        self.current_frame = None
        self.lock = threading.Lock()
        self.is_running = False
        
        # 初始化摄像头
        print("初始化摄像头...")
        self.cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        
        if self.cap.isOpened():
            print("摄像头初始化成功")
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.start_capture()
        else:
            print("摄像头初始化失败")
    
    def start_capture(self):
        self.is_running = True
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        print("摄像头捕获线程已启动")
    
    def _capture_loop(self):
        frame_count = 0
        while self.is_running and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            if ret:
                with self.lock:
                    self.current_frame = frame.copy()
                frame_count += 1
                if frame_count % 100 == 0:
                    print(f"已捕获 {frame_count} 帧")
            time.sleep(0.033)  # 30 FPS
    
    def get_frame_jpeg(self):
        with self.lock:
            if self.current_frame is not None:
                _, buffer = cv2.imencode('.jpg', self.current_frame)
                return buffer.tobytes()
        return None
    
    def get_frame_base64(self):
        with self.lock:
            if self.current_frame is not None:
                _, buffer = cv2.imencode('.jpg', self.current_frame)
                return base64.b64encode(buffer).decode('utf-8')
        return None

# 创建摄像头管理器
camera_manager = SimpleCameraManager()

def generate_video_stream():
    """生成MJPEG视频流"""
    while True:
        frame_data = camera_manager.get_frame_jpeg()
        if frame_data:
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_data + b'\r\n')
        else:
            time.sleep(0.1)

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'camera_available': camera_manager.cap.isOpened() if camera_manager.cap else False})

@app.route('/video_stream')
def video_stream():
    """提供MJPEG视频流"""
    return Response(generate_video_stream(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture_frame')
def capture_frame():
    """捕获当前帧"""
    frame_data = camera_manager.get_frame_base64()
    if frame_data:
        return jsonify({
            'success': True,
            'image_data': frame_data,
            'timestamp': time.time()
        })
    else:
        return jsonify({
            'success': False,
            'error': 'No frame available'
        }), 500

if __name__ == '__main__':
    print("启动简化视频服务器...")
    app.run(host='127.0.0.1', port=5001, debug=False)
