# 摄像头资源冲突修复方案

## 问题描述

PraiseMirror项目中存在摄像头资源冲突问题：

1. **资源独占冲突**：opencv_service.py中的`cv2.VideoCapture(0, cv2.CAP_DSHOW)`独占摄像头资源
2. **前端访问失败**：浏览器的`getUserMedia()`因摄像头被占用而失败
3. **多线程竞争**：detect_human()线程和video_stream() API同时访问VideoCapture对象
4. **线程安全问题**：OpenCV的VideoCapture对象不是线程安全的

## 解决方案

### 1. 统一摄像头管理架构

创建`CameraManager`类统一管理摄像头访问：

```python
class CameraManager:
    """线程安全的摄像头管理器"""
    
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.cap = None
        self.lock = threading.Lock()
        self.current_frame = None
        self.frame_available = threading.Event()
        self.is_running = False
        self.capture_thread = None
```

### 2. 线程安全的帧管理

- 使用`threading.Lock()`确保线程安全
- 单一捕获线程持续读取摄像头数据
- 共享帧缓冲区供其他组件访问

### 3. HTTP视频流传输

添加新的API端点：

- `/video_stream`：提供MJPEG格式的实时视频流
- `/capture_frame`：捕获当前帧并返回base64编码数据

### 4. 前端架构调整

- 移除前端的`getUserMedia()`调用
- 使用`<img>`标签接收后端视频流
- 通过API获取图像数据进行分析

## 修改文件清单

### 后端修改

1. **opencv_service.py**
   - 添加`CameraManager`类
   - 修改`detect_human()`函数使用新的管理器
   - 添加`/video_stream`和`/capture_frame`端点

### 前端修改

2. **src/components/CameraPreview.tsx**
   - 移除`getUserMedia()`调用
   - 改用`<img>`标签显示后端视频流
   - 添加连接状态指示

3. **src/App.tsx**
   - 移除摄像头访问相关代码
   - 修改`captureImage()`函数调用后端API
   - 移除隐藏的`<video>`元素

## 技术优势

1. **资源统一管理**：单一摄像头访问点，避免资源冲突
2. **线程安全**：使用锁机制保护共享资源
3. **架构清晰**：前后端职责分离，后端负责摄像头管理
4. **性能优化**：减少重复的摄像头初始化
5. **错误处理**：统一的错误处理和重连机制

## 测试验证

### 预期结果

- ✅ OpenCV服务正常启动并占用摄像头
- ✅ 前端能够接收后端视频流
- ✅ 人体检测功能正常工作
- ✅ 前端`getUserMedia()`失败（证明资源被后端占用）
- ✅ 图像捕获和分析流程正常

### 测试方法

1. 启动OpenCV服务：`python opencv_service.py`
2. 访问测试页面：`test_video_stream.html`
3. 验证视频流显示正常
4. 确认前端摄像头访问失败
5. 测试帧捕获API功能

## 部署说明

1. 确保OpenCV环境正确配置
2. 启动后端服务：`python opencv_service.py`
3. 启动前端应用：`npm start`
4. 验证摄像头权限和设备可用性

## 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查摄像头设备是否被其他应用占用
   - 确认摄像头驱动程序正常
   - 尝试不同的摄像头索引（0, 1, 2...）

2. **视频流无法显示**
   - 检查Flask服务是否正常运行
   - 确认CORS配置正确
   - 验证网络连接和端口访问

3. **人体检测异常**
   - 检查Haar级联分类器文件
   - 确认摄像头画质和光线条件
   - 验证检测线程是否正常运行

## 后续优化

1. **性能优化**：调整帧率和分辨率参数
2. **错误恢复**：添加摄像头断线重连机制
3. **多摄像头支持**：扩展支持多个摄像头设备
4. **WebSocket流**：考虑使用WebSocket提供更低延迟的视频流
