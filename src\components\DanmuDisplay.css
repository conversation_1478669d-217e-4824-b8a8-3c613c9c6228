.danmu-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: black;
  overflow: hidden;
}

.danmu-item {
  position: absolute;
  white-space: nowrap;
  color: var(--danmu-color, white);
  font-size: var(--danmu-size, 16px);
  top: var(--danmu-top, 0);
  left: var(--danmu-position, 0);
  transform: translateX(0);
}

.danmu-status-indicator {
  position: absolute;
  top: 1rem;
  left: 1rem;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 0.75rem;
  border-radius: 0.5rem;
}

.danmu-status-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.danmu-status-dot.active {
  background-color: #10B981; /* green-500 */
}

.danmu-status-dot.inactive {
  background-color: #EF4444; /* red-500 */
}

.danmu-status-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}