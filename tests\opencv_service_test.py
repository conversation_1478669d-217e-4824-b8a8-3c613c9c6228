import requests
import time
import cv2
import base64
import numpy as np

def test_opencv_service():
    """测试OpenCV服务功能"""
    print("测试OpenCV服务...")
    
    # 1. 测试健康检查
    try:
        response = requests.get('http://127.0.0.1:5000/health', timeout=5)
        print(f"健康检查: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False
    
    # 2. 测试检测状态
    try:
        response = requests.get('http://127.0.0.1:5000/detection_status', timeout=5)
        print(f"检测状态: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"检测状态查询失败: {e}")
    
    # 3. 测试匿名化功能
    try:
        # 创建一个简单的测试图像
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        _, buffer = cv2.imencode('.jpg', test_img)
        test_image_data = base64.b64encode(buffer).decode('utf-8')
        
        response = requests.post('http://127.0.0.1:5000/anonymize', 
                               json={'image_data': test_image_data},
                               timeout=10)
        print(f"匿名化测试: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            if 'anonymized_image' in data:
                print("  匿名化功能正常")
            elif 'error' in data:
                print(f"  匿名化出错: {data['error']}")
        else:
            print(f"  匿名化请求失败: {response.text}")
    except Exception as e:
        print(f"匿名化测试失败: {e}")

    return True

def capture_and_test_with_real_camera():
    """使用真实摄像头测试"""
    print("\n使用真实摄像头测试...")
    
    # 初始化摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("无法打开摄像头")
        return
    
    print("摄像头已打开，尝试捕获图像...")
    
    # 读取一帧
    ret, frame = cap.read()
    if not ret:
        print("无法从摄像头读取图像")
        cap.release()
        return
    
    print("成功捕获图像")
    
    # 尝试人脸检测
    try:
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        print(f"检测到 {len(faces)} 个人脸")
    except Exception as e:
        print(f"人脸检测出错: {e}")
    
    # 编码图像用于测试服务
    _, buffer = cv2.imencode('.jpg', frame)
    image_data = base64.b64encode(buffer).decode('utf-8')
    
    # 测试服务
    try:
        response = requests.post('http://127.0.0.1:5000/anonymize',
                               json={'image_data': image_data},
                               timeout=10)
        if response.status_code == 200:
            print("图像匿名化服务工作正常")
        else:
            print(f"图像匿名化服务返回错误: {response.status_code}")
    except Exception as e:
        print(f"测试图像匿名化服务时出错: {e}")
    
    cap.release()

if __name__ == "__main__":
    print("OpenCV服务功能测试")
    print("=" * 30)
    
    success = test_opencv_service()
    
    if success:
        print("\n基础服务测试完成，现在测试摄像头功能...")
        capture_and_test_with_real_camera()
    else:
        print("基础服务测试失败")