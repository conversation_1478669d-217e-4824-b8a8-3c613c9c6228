import unittest
from unittest.mock import Mock, patch
import sys
import os

# Add src to path to import React components
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

# Mock React and other dependencies
class MockReact:
    useState = lambda x: [x, lambda y: None]
    useEffect = lambda f, d=None: None
    useRef = lambda x=None: Mock()
    FC = lambda x: x

sys.modules['react'] = MockReact()

# Mock CSS module
class MockCSS:
    pass

sys.modules['./DanmuDisplay.css'] = MockCSS()

# Now import the component
from components.DanmuDisplay import DanmuDisplay, DanmuItem

class TestDanmuDisplay(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.praises = ["你真漂亮！", "穿搭太棒了！", "气质真好！"]
    
    def test_danmu_item_creation(self):
        """Test DanmuItem creation with valid parameters"""
        danmu = DanmuItem(
            id=1,
            text="测试弹幕",
            color="red",
            size=20,
            top=100,
            speed=2.5,
            position=500
        )
        
        self.assertEqual(danmu.id, 1)
        self.assertEqual(danmu.text, "测试弹幕")
        self.assertEqual(danmu.color, "red")
        self.assertEqual(danmu.size, 20)
        self.assertEqual(danmu.top, 100)
        self.assertEqual(danmu.speed, 2.5)
        self.assertEqual(danmu.position, 500)
    
    @patch('components.DanmuDisplay.useState')
    @patch('components.DanmuDisplay.useEffect')
    @patch('components.DanmuDisplay.useRef')
    def test_component_initialization(self, mock_useRef, mock_useEffect, mock_useState):
        """Test component initialization with mocked hooks"""
        # Mock hooks
        mock_useState.side_effect = [
            [[], lambda x: None],  # danmuList
            [True, lambda x: None]  # detectionStatus
        ]
        mock_useRef.return_value = Mock()
        
        # Create component instance
        component = DanmuDisplay(praises=self.praises)
        
        # Verify hooks were called
        self.assertEqual(mock_useState.call_count, 2)
        self.assertEqual(mock_useRef.call_count, 4)
        self.assertEqual(mock_useEffect.call_count, 4)
    
    def test_danmu_props_validation(self):
        """Test that DanmuDisplay validates props correctly"""
        # Test with empty praises
        component = DanmuDisplay(praises=[])
        
        # Test with None praises
        component = DanmuDisplay(praises=None)
        
        # Test with string praises (should handle gracefully)
        component = DanmuDisplay(praises="single praise")

class TestDanmuCSS(unittest.TestCase):
    
    def test_css_classes_exist(self):
        """Test that required CSS classes are defined"""
        # This test verifies that the CSS file contains expected classes
        css_file_path = os.path.join(os.path.dirname(__file__), '..', 'src', 'components', 'DanmuDisplay.css')
        
        self.assertTrue(os.path.exists(css_file_path), "CSS file should exist")
        
        with open(css_file_path, 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        # Check for required CSS classes
        required_classes = [
            '.danmu-container',
            '.danmu-item',
            '.danmu-status-indicator',
            '.danmu-status-dot',
            '.danmu-status-text'
        ]
        
        for css_class in required_classes:
            self.assertIn(css_class, css_content, f"CSS class {css_class} should be defined")

if __name__ == '__main__':
    unittest.main()