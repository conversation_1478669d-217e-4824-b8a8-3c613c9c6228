// src/components/StandbyDisplay.tsx
import React, { useState, useEffect } from 'react';

interface StandbyDisplayProps {
  onConfigClick?: () => void; // Optional config button handler
}

const StandbyDisplay: React.FC<StandbyDisplayProps> = ({ onConfigClick }) => {
  const [detectionStatus, setDetectionStatus] = useState<boolean>(false);
  const [lastChecked, setLastChecked] = useState<string>('');

  // 定期检查检测状态
  useEffect(() => {
    const checkDetectionStatus = async () => {
      try {
        const response = await fetch('http://127.0.0.1:5000/detection_status');
        const data = await response.json();
        setDetectionStatus(data.human_detected);
        setLastChecked(new Date().toLocaleTimeString());
      } catch (error) {
        console.error('无法获取检测状态:', error);
      }
    };

    // 初始检查
    checkDetectionStatus();
    
    // 每秒检查一次
    const interval = setInterval(checkDetectionStatus, 1000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative w-full h-full bg-black flex items-center justify-center">
      {/* Brand Logo or Standby Animation Placeholder */}
      <div className="text-white text-4xl font-bold animate-pulse">
        Smart Praise Mirror
      </div>
      
      {/* Detection Status Indicator */}
      <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white p-3 rounded-lg">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${detectionStatus ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span>
            {detectionStatus ? '检测到人体' : '未检测到人体'}
          </span>
        </div>
        <div className="text-xs mt-1 text-gray-300">
          最后检查: {lastChecked}
        </div>
      </div>
      
      {/* Optional Config Button in Corner */}
      {onConfigClick && (
        <button 
          onClick={onConfigClick}
          className="absolute bottom-4 right-4 bg-gray-800 text-white p-2 rounded hover:bg-gray-700 transition-colors"
        >
          Config
        </button>
      )}
    </div>
  );
};

export default StandbyDisplay;