<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频流测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        .video-stream {
            max-width: 100%;
            border: 2px solid #333;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>摄像头资源冲突修复测试</h1>
        
        <div class="status" id="status">
            正在检查服务状态...
        </div>
        
        <div class="video-container">
            <h3>后端视频流（应该正常显示）</h3>
            <img id="videoStream" class="video-stream" src="http://127.0.0.1:5001/video_stream" 
                 alt="视频流" style="display: none;">
            <div id="videoError" style="display: none; color: red;">
                无法加载视频流
            </div>
        </div>
        
        <div class="video-container">
            <h3>前端摄像头访问测试（应该失败，证明资源被后端占用）</h3>
            <video id="frontendVideo" width="640" height="480" autoplay muted style="display: none;"></video>
            <div id="frontendError" style="display: none; color: red;">
                前端摄像头访问失败（这是预期的）
            </div>
        </div>
        
        <div>
            <button onclick="testBackendStream()">测试后端视频流</button>
            <button onclick="testFrontendCamera()">测试前端摄像头访问</button>
            <button onclick="testCaptureFrame()">测试帧捕获</button>
            <button onclick="checkHealth()">检查服务健康状态</button>
        </div>
        
        <div class="test-results" id="testResults">
            <h3>测试结果</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        let results = [];
        
        function addResult(test, status, message) {
            results.push({
                test: test,
                status: status,
                message: message,
                timestamp: new Date().toLocaleTimeString()
            });
            updateResults();
        }
        
        function updateResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = results.map(r => 
                `<div style="margin: 5px 0; padding: 5px; background: ${r.status === 'success' ? '#d4edda' : '#f8d7da'}; border-radius: 3px;">
                    <strong>${r.test}</strong> [${r.timestamp}]: ${r.message}
                </div>`
            ).join('');
        }
        
        function updateStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
        }
        
        async function checkHealth() {
            try {
                const response = await fetch('http://127.0.0.1:5001/health');
                const data = await response.json();
                addResult('健康检查', 'success', `服务状态: ${data.status}, 摄像头可用: ${data.camera_available}`);
                updateStatus(`服务正常运行，摄像头${data.camera_available ? '可用' : '不可用'}`);
            } catch (error) {
                addResult('健康检查', 'error', `服务连接失败: ${error.message}`);
                updateStatus('服务连接失败', true);
            }
        }
        
        function testBackendStream() {
            const img = document.getElementById('videoStream');
            const errorDiv = document.getElementById('videoError');
            
            img.onload = function() {
                img.style.display = 'block';
                errorDiv.style.display = 'none';
                addResult('后端视频流', 'success', '视频流加载成功');
            };
            
            img.onerror = function() {
                img.style.display = 'none';
                errorDiv.style.display = 'block';
                addResult('后端视频流', 'error', '视频流加载失败');
            };
            
            // 重新加载图像
            img.src = img.src + '?t=' + Date.now();
        }
        
        async function testFrontendCamera() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                const video = document.getElementById('frontendVideo');
                const errorDiv = document.getElementById('frontendError');
                
                video.srcObject = stream;
                video.style.display = 'block';
                errorDiv.style.display = 'none';
                addResult('前端摄像头', 'error', '前端摄像头访问成功（这表明资源冲突未解决）');
                
                // 停止流
                setTimeout(() => {
                    stream.getTracks().forEach(track => track.stop());
                }, 3000);
                
            } catch (error) {
                const video = document.getElementById('frontendVideo');
                const errorDiv = document.getElementById('frontendError');
                
                video.style.display = 'none';
                errorDiv.style.display = 'block';
                errorDiv.textContent = `前端摄像头访问失败: ${error.message}（这是预期的）`;
                addResult('前端摄像头', 'success', `前端摄像头访问失败: ${error.message}（证明资源被后端占用）`);
            }
        }
        
        async function testCaptureFrame() {
            try {
                const response = await fetch('http://127.0.0.1:5001/capture_frame');
                const data = await response.json();
                
                if (data.success) {
                    addResult('帧捕获', 'success', `成功捕获帧，数据大小: ${data.image_data.length} 字符`);
                } else {
                    addResult('帧捕获', 'error', `帧捕获失败: ${data.error}`);
                }
            } catch (error) {
                addResult('帧捕获', 'error', `帧捕获请求失败: ${error.message}`);
            }
        }
        
        // 页面加载时自动检查健康状态
        window.onload = function() {
            checkHealth();
            testBackendStream();
        };
    </script>
</body>
</html>
