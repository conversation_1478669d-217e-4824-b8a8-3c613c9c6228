// src/hooks/usePraiseGenerator.ts

interface VisionAnalysisResult {
  description: string;
  clothing_items?: string[];
  colors?: string[];
  style?: string;
  confidence?: number;
}

const usePraiseGenerator = () => {
  // 具体赞美模板 - 基于具体的服装和颜色
  const specificPraiseTemplates = [
    "这件{item}真显气质！",
    "这个{item}的搭配太有品味了！",
    "{item}的{color}真是点睛之笔！",
    "{item}的款式非常时尚！",
    "{item}和整体造型完美融合！",
    "{item}的选择展现了你的好眼光！"
  ];
  
  // 通用赞美模板
  const generalPraiseTemplates = [
    "今天看起来超有魅力！",
    "自信放光芒，你就是最闪亮的星！",
    "这身打扮简直完美！",
    "气质出众，让人眼前一亮！",
    "穿搭品味一流，太会搭配了！",
    "整体造型满分，无可挑剔！",
    "时尚感爆棚，走在潮流前沿！",
    "这样的穿搭让人心情都变好了！",
    "完美展现个人风格，太棒了！",
    "今天的你格外耀眼动人！",
    "休闲中带着精致，非常得体的装扮！",
    "商务休闲风把握得恰到好处！",
    "既舒适又不失格调，完美平衡！",
    "专业干练，职场精英范儿十足！",
    "商务装扮彰显专业气质！",
    "职场穿搭典范，值得学习！",
    "优雅大方，气质非凡！",
    "高贵典雅，宛如画中走出！",
    "优雅风范，令人赏心悦目！",
    "休闲舒适，活力满满！",
    "日常穿搭也能如此有型！",
    "轻松自在，又不失时尚感！"
  ];
  
  // 风格特定的赞美
  const styleSpecificPraises: Record<string, string[]> = {
    "smart casual": [
      "休闲中带着精致，非常得体的装扮！",
      "商务休闲风把握得恰到好处！",
      "既舒适又不失格调，完美平衡！"
    ],
    "professional": [
      "专业干练，职场精英范儿十足！",
      "商务装扮彰显专业气质！",
      "职场穿搭典范，值得学习！"
    ],
    "elegant": [
      "优雅大方，气质非凡！",
      "高贵典雅，宛如画中走出！",
      "优雅风范，令人赏心悦目！"
    ],
    "casual": [
      "休闲舒适，活力满满！",
      "日常穿搭也能如此有型！",
      "轻松自在，又不失时尚感！"
    ]
  };

  // 基于视觉分析结果生成赞美
  const generatePraise = (visionResult: VisionAnalysisResult): string[] => {
    const praises: string[] = [];
    
    // 如果有具体的服装信息，生成具体赞美
    if (visionResult.clothing_items && visionResult.clothing_items.length > 0) {
      const specificPraises = specificPraiseTemplates.map(template => {
        const item = visionResult.clothing_items![Math.floor(Math.random() * visionResult.clothing_items!.length)];
        let praise = template.replace("{item}", item);
        
        if (visionResult.colors && visionResult.colors.length > 0 && praise.includes("{color}")) {
          const color = visionResult.colors![Math.floor(Math.random() * visionResult.colors!.length)];
          praise = praise.replace("{color}", color);
        }
        
        return praise;
      });
      
      // 选择1-2个具体赞美
      praises.push(...specificPraises.slice(0, Math.min(2, specificPraises.length)));
    }
    
    // 添加通用赞美
    const generalPraiseCount = 3 - praises.length;
    for (let i = 0; i < generalPraiseCount; i++) {
      const template = generalPraiseTemplates[Math.floor(Math.random() * generalPraiseTemplates.length)];
      praises.push(template);
    }
    
    return praises;
  };

  return { generatePraise };
};

export default usePraiseGenerator;