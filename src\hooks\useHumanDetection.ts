// src/hooks/useHumanDetection.ts
import { useState, useEffect, useRef } from 'react';

const useHumanDetection = (onHumanDetected: () => void) => {
  const [isDetecting, setIsDetecting] = useState(false);
  const detectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const consecutiveDetectionCount = useRef(0);
  const lastDetectionTimeRef = useRef<number>(0);

  // 检查OpenCV服务状态
  const checkDetectionStatus = async () => {
    try {
      const response = await fetch('http://127.0.0.1:5000/detection_status');
      const data = await response.json();
      
      const currentTime = Date.now();
      
      if (data.human_detected) {
        consecutiveDetectionCount.current++;
        console.log(`OpenCV检测到人体 (连续: ${consecutiveDetectionCount.current})`);
        
        // 连续检测到3次才触发，避免误检
        if (consecutiveDetectionCount.current >= 3) {
          // 限制触发频率，至少间隔5秒
          if (currentTime - lastDetectionTimeRef.current > 5000) {
            handleHumanDetected();
            lastDetectionTimeRef.current = currentTime;
          }
        }
      } else {
        consecutiveDetectionCount.current = 0;
      }
    } catch (error) {
      console.error('OpenCV服务连接失败:', error);
      consecutiveDetectionCount.current = 0;
      
      // 降级到模拟检测（仅用于开发测试）
      // 保留这个选项用于开发测试，但使用更合理的概率
      if (Math.random() > 0.95) { // 5% 概率模拟触发
        console.log("模拟检测到人体");
        handleHumanDetected();
      }
    }
  };

  const handleHumanDetected = () => {
    if (detectionTimeoutRef.current) {
      clearTimeout(detectionTimeoutRef.current);
    }
    
    detectionTimeoutRef.current = setTimeout(() => {
      console.log("触发onHumanDetected回调");
      onHumanDetected();
      setIsDetecting(false);
      consecutiveDetectionCount.current = 0; // 重置计数
    }, 500); // 0.5 seconds
    
    setIsDetecting(true);
  };

  useEffect(() => {
    console.log("启动人体检测服务");
    const interval = setInterval(checkDetectionStatus, 1000); // 每1秒检查一次
    
    return () => {
      console.log("清理人体检测服务");
      clearInterval(interval);
      if (detectionTimeoutRef.current) {
        clearTimeout(detectionTimeoutRef.current);
      }
    };
  }, [onHumanDetected]);

  return { isDetecting };
};

export default useHumanDetection;