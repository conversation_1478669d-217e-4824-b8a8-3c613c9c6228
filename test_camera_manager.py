#!/usr/bin/env python3
"""
测试CameraManager类的基本功能
"""

import cv2
import numpy as np
import base64
import threading
import time
from queue import Queue

class CameraManager:
    """线程安全的摄像头管理器"""
    
    def __init__(self, camera_index=0):
        self.camera_index = camera_index
        self.cap = None
        self.lock = threading.Lock()
        self.current_frame = None
        self.frame_available = threading.Event()
        self.is_running = False
        self.capture_thread = None
        
        # 初始化摄像头
        self._initialize_camera()
        
        # 启动帧捕获线程
        self.start_capture()
    
    def _initialize_camera(self):
        """初始化摄像头"""
        print("正在初始化摄像头...")
        self.cap = cv2.VideoCapture(self.camera_index, cv2.CAP_DSHOW)
        
        if not self.cap.isOpened():
            print("错误：无法打开摄像头")
            return False
        
        print("摄像头初始化成功")
        # 设置摄像头参数
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        print(f"摄像头分辨率: {self.cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
        print(f"摄像头FPS: {self.cap.get(cv2.CAP_PROP_FPS)}")
        
        # 测试读取一帧
        print("测试读取摄像头帧...")
        for i in range(5):
            ret, frame = self.cap.read()
            if ret:
                print(f"成功读取测试帧，尺寸: {frame.shape}")
                return True
            else:
                print(f"无法读取测试帧 {i+1}")
                time.sleep(0.5)
        
        print("警告：摄像头可能无法正常工作")
        return False
    
    def start_capture(self):
        """启动帧捕获线程"""
        if self.is_running:
            return
        
        self.is_running = True
        self.capture_thread = threading.Thread(target=self._capture_loop, daemon=True)
        self.capture_thread.start()
        print("摄像头捕获线程已启动")
    
    def _capture_loop(self):
        """持续捕获帧的循环"""
        frame_count = 0
        while self.is_running and self.cap and self.cap.isOpened():
            ret, frame = self.cap.read()
            frame_count += 1
            
            if ret:
                with self.lock:
                    self.current_frame = frame.copy()
                    self.frame_available.set()
                
                # 每100帧打印一次成功信息
                if frame_count % 100 == 0:
                    print(f"成功捕获帧 {frame_count}")
            else:
                if frame_count % 50 == 0:
                    print("无法从摄像头读取帧")
                time.sleep(0.1)
            
            time.sleep(0.033)  # 约30FPS
    
    def get_frame(self):
        """获取当前帧（线程安全）"""
        with self.lock:
            if self.current_frame is not None:
                return True, self.current_frame.copy()
            return False, None
    
    def stop(self):
        """停止摄像头管理器"""
        self.is_running = False
        if self.capture_thread:
            self.capture_thread.join()
        if self.cap:
            self.cap.release()
        print("摄像头管理器已停止")

def test_camera_manager():
    """测试CameraManager"""
    print("开始测试CameraManager...")
    
    # 创建摄像头管理器
    camera_manager = CameraManager()
    
    # 等待几秒让捕获线程运行
    time.sleep(3)
    
    # 测试获取帧
    for i in range(5):
        ret, frame = camera_manager.get_frame()
        if ret:
            print(f"测试 {i+1}: 成功获取帧，尺寸: {frame.shape}")
        else:
            print(f"测试 {i+1}: 无法获取帧")
        time.sleep(1)
    
    # 停止管理器
    camera_manager.stop()
    print("测试完成")

if __name__ == "__main__":
    test_camera_manager()
