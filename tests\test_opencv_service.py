import requests
import time

def test_opencv_service():
    try:
        # 测试健康检查
        response = requests.get('http://127.0.0.1:5000/health', timeout=2)
        print(f"健康检查: {response.status_code} - {response.json()}")
        
        # 测试检测状态
        response = requests.get('http://127.0.0.1:5000/detection_status', timeout=2)
        print(f"检测状态: {response.status_code} - {response.json()}")
        
        return True
    except requests.exceptions.RequestException as e:
        print(f"服务连接失败: {e}")
        return False

if __name__ == "__main__":
    print("测试OpenCV服务...")
    success = test_opencv_service()
    if success:
        print("OpenCV服务运行正常")
    else:
        print("OpenCV服务连接失败")