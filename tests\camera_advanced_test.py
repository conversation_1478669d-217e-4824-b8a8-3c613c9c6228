import cv2
import time

def test_camera_functionality(camera_index):
    """测试指定索引的摄像头功能"""
    print(f"测试摄像头 {camera_index}...")
    
    # 初始化摄像头
    cap = cv2.VideoCapture(camera_index)
    
    if not cap.isOpened():
        print(f"无法打开摄像头 {camera_index}")
        return False
    
    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    print(f"摄像头 {camera_index} 参数:")
    print(f"  分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
    print(f"  FPS: {cap.get(cv2.CAP_PROP_FPS)}")
    
    # 读取几帧图像测试
    for i in range(10):
        ret, frame = cap.read()
        if ret:
            print(f"  成功读取第 {i+1} 帧")
        else:
            print(f"  第 {i+1} 帧读取失败")
            break
        time.sleep(0.1)
    
    # 释放摄像头
    cap.release()
    return ret

def main():
    print("摄像头功能测试:")
    
    # 检测可用摄像头数量
    camera_count = 0
    while True:
        cap = cv2.VideoCapture(camera_count)
        if not cap.isOpened():
            cap.release()
            break
        cap.release()
        camera_count += 1
    
    print(f"检测到 {camera_count} 个摄像头")
    
    # 测试每个摄像头的功能
    for i in range(camera_count):
        success = test_camera_functionality(i)
        if success:
            print(f"摄像头 {i} 功能正常\n")
        else:
            print(f"摄像头 {i} 功能异常\n")

if __name__ == "__main__":
    main()