// src/components/DanmuDisplay.tsx
import React, { useState, useEffect, useRef } from 'react';
import './DanmuDisplay.css';

interface DanmuItem {
  id: number;
  text: string;
  color: string;
  size: number;
  top: number;
  speed: number; // pixels per frame
  position: number; // current horizontal position (from right)
}

interface DanmuDisplayProps {
  praises: string[];
}

const DanmuDisplay: React.FC<DanmuDisplayProps> = ({ praises }) => {
  const [danmuList, setDanmuList] = useState<DanmuItem[]>([]);
  const [detectionStatus, setDetectionStatus] = useState<boolean>(true);
  const containerRef = useRef<HTMLDivElement>(null);
  const danmuIdRef = useRef(0);
  const animationFrameRef = useRef<number>(0);
  const containerWidthRef = useRef(0);
  const styleSheetRef = useRef<HTMLStyleElement | null>(null);

  // Update container width on resize
  useEffect(() => {
    const updateWidth = () => {
      if (containerRef.current) {
        containerWidthRef.current = containerRef.current.clientWidth;
      }
    };
    updateWidth();
    window.addEventListener('resize', updateWidth);
    return () => window.removeEventListener('resize', updateWidth);
  }, []);

  // 定期检查检测状态
  useEffect(() => {
    const checkDetectionStatus = async () => {
      try {
        const response = await fetch('http://127.0.0.1:5000/detection_status');
        const data = await response.json();
        setDetectionStatus(data.human_detected);
      } catch (error) {
        console.error('无法获取检测状态:', error);
      }
    };

    // 每秒检查一次
    const interval = setInterval(checkDetectionStatus, 1000);
    
    return () => clearInterval(interval);
  }, []);

  // Initialize dynamic styles
  useEffect(() => {
    // Create a style element for dynamic styles
    const styleElement = document.createElement('style');
    styleElement.id = 'danmu-dynamic-styles';
    document.head.appendChild(styleElement);
    styleSheetRef.current = styleElement;
    
    return () => {
      // Clean up the style element
      if (styleElement.parentNode) {
        styleElement.parentNode.removeChild(styleElement);
      }
    };
  }, []);

  // Function to update dynamic styles
  const updateDynamicStyles = (danmu: DanmuItem) => {
    if (styleSheetRef.current) {
      const styleSheet = styleSheetRef.current.sheet;
      if (styleSheet) {
        // Remove existing rule for this danmu if it exists
        const ruleIndex = Array.from(styleSheet.cssRules).findIndex(
          rule => rule.cssText.startsWith(`.danmu-${danmu.id}-`)
        );
        if (ruleIndex !== -1) {
          styleSheet.deleteRule(ruleIndex);
        }

        // Add new rule with updated styles
        const rule = `
          .danmu-${danmu.id}-item {
            --danmu-color: ${danmu.color};
            --danmu-size: ${danmu.size}px;
            --danmu-top: ${danmu.top}px;
            --danmu-position: ${danmu.position}px;
          }
        `;
        styleSheet.insertRule(rule, styleSheet.cssRules.length);
      }
    }
  };

  // Function to add a new danmu
  const addDanmu = (text: string) => {
    if (containerRef.current) {
      const newDanmu: DanmuItem = {
        id: danmuIdRef.current++,
        text,
        color: `hsl(${Math.floor(Math.random() * 360)}, 100%, 50%)`,
        size: Math.floor(Math.random() * 20) + 30, // 30-50px
        top: Math.random() * (containerRef.current.clientHeight - 50), // Random vertical position
        speed: Math.random() * 2 + 1, // Random speed (1-3 px/frame)
        position: containerWidthRef.current, // Start from the right edge
      };
      
      // Add dynamic styles for this danmu
      updateDynamicStyles(newDanmu);
      
      setDanmuList(prev => [...prev, newDanmu]);
    }
  };

  // Add praises as danmu when they change
  useEffect(() => {
    if (praises.length > 0) {
      let index = 0;
      const addPraise = () => {
        if (index < praises.length) {
          addDanmu(praises[index]);
          index++;
          setTimeout(addPraise, 300); // Add next praise after 300ms
        }
      };
      addPraise();
    }
  }, [praises]);

  // Animation loop for moving danmu
  useEffect(() => {
    const animate = () => {
      setDanmuList(prev => {
        return prev
          .map(danmu => {
            // Move danmu left
            const newPosition = danmu.position - danmu.speed;
            
            // Update dynamic styles with new position
            const updatedDanmu = { ...danmu, position: newPosition };
            updateDynamicStyles(updatedDanmu);
            
            return updatedDanmu;
          })
          .filter(danmu => danmu.position > -500); // Remove danmu that are far off-screen
      });
      
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    
    animationFrameRef.current = requestAnimationFrame(animate);
    
    return () => {
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, []);

  return (
    <div ref={containerRef} className="danmu-container">
      {danmuList.map((danmu) => (
        <div
          key={danmu.id}
          className={`danmu-item danmu-${danmu.id}-item`}
        >
          {danmu.text}
        </div>
      ))}
      
      {/* Detection Status Indicator */}
      <div className="danmu-status-indicator">
        <div className="danmu-status-text">
          <div className={`danmu-status-dot ${detectionStatus ? 'active' : 'inactive'}`}></div>
          <span>
            {detectionStatus ? '检测到人体' : '未检测到人体'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default DanmuDisplay;